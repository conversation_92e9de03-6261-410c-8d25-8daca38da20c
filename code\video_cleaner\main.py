"""
Main entry point for the video cleaner application.
"""

import argparse
import os
import sys
import time
from typing import Optional

from .core.config import ProcessingConfig, get_config, set_config
from .core.transcription import load_foul_words, transcribe_audio, find_mute_segments_verified
from .core.audio_processor import extract_audio
from .core.video_processor import create_muted_video
from .core.subtitle_parser import parse_subtitles
from .utils.logging import setup_logging, get_logger
from .utils.cleanup import register_temp_file, safe_remove_file
from .gui.main_window import run_gui


def generate_report(results_data, args, output_path: str, processing_time: float) -> None:
    """Generate a detailed processing report."""
    logger = get_logger()
    config = get_config()
    
    # Generate report file path
    base_filename = os.path.splitext(os.path.basename(args.video_path))[0]
    input_dir = os.path.dirname(args.video_path)
    report_file = os.path.join(input_dir, f"{base_filename}{config.report_suffix}")
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("=" * 50 + "\n")
            f.write("VIDEO CLEANER - PROCESSING REPORT\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"Input Video: {args.video_path}\n")
            f.write(f"Output Video: {output_path}\n")
            f.write(f"Subtitle File: {getattr(args, 'srt_path', 'N/A')}\n")
            f.write(f"Foul Words File: {args.words_file}\n")
            f.write(f"Whisper Model: {args.model}\n")
            f.write(f"Processing Time: {processing_time:.2f} seconds\n\n")
            
            # Statistics - handle both new and old data structures
            if hasattr(results_data, 'stats'):
                stats = results_data.stats
                mute_segments = results_data.mute_segments
                verification_details = results_data.verification_details
            else:
                # Backward compatibility
                stats = results_data.get("stats", {})
                mute_segments = results_data.get("mute_segments", [])
                verification_details = results_data.get("verification_details", [])

            f.write("--- Processing Statistics ---\n")

            # Determine processing mode
            subtitle_file = getattr(args, 'srt_path', 'N/A')
            if subtitle_file and subtitle_file != 'N/A':
                f.write(f"Processing Mode: Subtitle Verification\n")
                f.write(f"Whisper - Potential Foul Words Found: {getattr(stats, 'whisper_potential', stats.get('whisper_potential', 0))}\n")
                f.write(f"Verification - Instances Verified for Mute: {getattr(stats, 'verified_for_mute', stats.get('verified_for_mute', 0))}\n")
                f.write(f"Verification - Skipped (Text Mismatch): {getattr(stats, 'skipped_text_mismatch', stats.get('skipped_text_mismatch', 0))}\n")
                f.write(f"Verification - Skipped (No Overlapping Subtitle): {getattr(stats, 'skipped_no_subtitle', stats.get('skipped_no_subtitle', 0))}\n")
            else:
                f.write(f"Processing Mode: AI-Only Detection\n")
                f.write(f"Whisper - Potential Foul Words Found: {getattr(stats, 'whisper_potential', stats.get('whisper_potential', 0))}\n")
                f.write(f"AI-Only - Instances Muted: {getattr(stats, 'ai_only_muted', stats.get('ai_only_muted', 0))}\n")

            f.write(f"Final Mute Segments: {len(mute_segments)}\n")
            f.write("-" * 30 + "\n\n")
            
            # Mute segments
            f.write("--- Mute Segments Applied ---\n")
            if mute_segments:
                total_muted_time = sum(end - start for start, end in mute_segments)
                f.write(f"Total Muted Duration: {total_muted_time:.3f} seconds\n")
                for i, (start, end) in enumerate(mute_segments, 1):
                    f.write(f"{i}. {start:.3f}s - {end:.3f}s (duration: {end-start:.3f}s)\n")
            else:
                f.write("No segments were muted.\n")
            f.write("-" * 30 + "\n\n")

            # Verification details
            f.write("--- Verification Details ---\n")
            if verification_details:
                for item in verification_details:
                    # Handle both new and old data structures
                    if hasattr(item, 'word'):
                        word = item.word
                        start = item.start
                        end = item.end
                        subtitle_text = item.subtitle_text
                        verified = item.verified
                        reason = item.reason
                    else:
                        word = item.get('word', '')
                        start = item.get('start', 0)
                        end = item.get('end', 0)
                        subtitle_text = item.get('subtitle_text', '')
                        verified = item.get('verified', False)
                        reason = item.get('reason', '')

                    f.write(f"- Word: '{word}' ({start:.3f}s - {end:.3f}s)\n")
                    f.write(f"  Subtitle: \"{subtitle_text}\"\n")
                    f.write(f"  Verified: {verified} ({reason})\n")
            else:
                f.write("No verification performed (likely due to missing subtitles or no Whisper detections).\n")
            f.write("-" * 30 + "\n\n")
        
        logger.info(f"Processing report saved to: {report_file}")
        
    except Exception as e:
        logger.error(f"Failed to generate report: {e}")


def process_video_cli(args) -> bool:
    """Process video using command line arguments."""
    logger = get_logger()
    config = get_config()
    
    start_time = time.time()
    success = False
    
    try:
        # 1. Load Foul Words
        logger.info("-" * 20 + " Step 1: Load Foul Words " + "-" * 20)
        foul_words = load_foul_words(args.words_file)
        if foul_words is None:
            raise RuntimeError("Failed to load foul words.")
        if not foul_words:
            logger.warning("Foul words list is empty. No words will be muted.")

        # 2. Parse Subtitles (if provided)
        logger.info("-" * 20 + " Step 2: Parse Subtitles " + "-" * 20)
        if args.srt_path:
            logger.info(f"Subtitle file provided: '{args.srt_path}' - will use subtitle verification mode.")
            subtitles = parse_subtitles(args.srt_path)
            if subtitles is None:
                raise RuntimeError("Failed to parse subtitles.")
        else:
            logger.info("No subtitle file provided - will use AI-only detection mode.")
            subtitles = None

        # 3. Extract Audio
        logger.info("-" * 20 + " Step 3: Extract Audio " + "-" * 20)
        if not extract_audio(args.video_path, config.temp_audio_filename):
            raise RuntimeError("Failed to extract audio from video.")

        # 4. Transcribe Audio
        logger.info("-" * 20 + " Step 4: Transcribe Audio " + "-" * 20)
        word_timestamps = transcribe_audio(config.temp_audio_filename, args.model)
        if word_timestamps is None:
            raise RuntimeError("Audio transcription failed.")

        # 5. Find Mute Segments (Verified or AI-only)
        if subtitles:
            logger.info("-" * 20 + " Step 5: Verify and Find Mute Segments " + "-" * 20)
        else:
            logger.info("-" * 20 + " Step 5: Find Mute Segments (AI-only) " + "-" * 20)
        results_data = find_mute_segments_verified(word_timestamps, foul_words or [], subtitles)
        mute_segments = results_data.mute_segments if hasattr(results_data, 'mute_segments') else results_data.get("mute_segments", [])

        # 6. Create Muted Video
        logger.info("-" * 20 + " Step 6: Create Muted Video " + "-" * 20)
        if mute_segments:
            if not create_muted_video(args.video_path, args.output_path, mute_segments):
                raise RuntimeError("Failed to create the final muted video.")
            else:
                logger.info(f"Muted video successfully created at '{args.output_path}'")
                success = True
        else:
            logger.info("No verified foul words found requiring muting. Output video was not created/modified.")
            success = True

        # 7. Generate Report
        logger.info("-" * 20 + " Step 7: Generate Report " + "-" * 20)
        processing_time = time.time() - start_time
        generate_report(results_data, args, args.output_path, processing_time)

        logger.info(f"Processing completed successfully in {processing_time:.2f} seconds!")
        return True

    except Exception as e:
        logger.exception(f"Processing failed: {e}")
        return False
        
    finally:
        # Clean up temporary audio file
        config = get_config()
        safe_remove_file(config.temp_audio_filename)


def main():
    """Main entry point for the application."""
    parser = argparse.ArgumentParser(
        description="Mute specified foul words in a video file. Optionally verify with an SRT subtitle file for improved accuracy. Generates log and report files.",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument("--gui", action="store_true", help="Launch the GUI version of the application.")
    parser.add_argument("video_path", nargs="?", help="Path to the input video file.")
    parser.add_argument("srt_path", nargs="?", help="Path to the corresponding SRT subtitle file (optional - if not provided, uses AI-only detection).")
    parser.add_argument("-w", "--words_file", default="foul_words.txt", help="Path to the text file containing words to mute.")
    parser.add_argument("-m", "--model", default="medium", help="Whisper model size (e.g., tiny, base, small, medium, large).")
    parser.add_argument("-o", "--output_path", default=None, help="Path to save the output muted video file. Default: [input_filename]_muted_verified.[ext]")
    parser.add_argument("--report_file", default=None, help="Path to save the processing report. Default: [input_filename]_report.log")
    parser.add_argument("--log_file", default=None, help="Path to save the detailed processing log. Default: [input_filename]_processing.log")
    parser.add_argument("--log_level", default="INFO", choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"], help="Set the logging level.")
    parser.add_argument("--config", default=None, help="Path to configuration file.")

    args = parser.parse_args()

    # Load configuration if provided
    if args.config:
        config = ProcessingConfig.load_from_file(args.config)
        set_config(config)

    # Check if GUI mode is requested or if no arguments provided
    if args.gui or not args.video_path:
        run_gui()
        return

    # Validate required arguments for command-line mode
    if not args.video_path:
        parser.error("video_path is required when not using --gui mode")
        return

    # Generate default output path if not provided
    if not args.output_path:
        base_name = os.path.splitext(args.video_path)[0]
        extension = os.path.splitext(args.video_path)[1]
        args.output_path = f"{base_name}{get_config().output_suffix}{extension}"

    # Generate log file path
    if not args.log_file:
        base_filename = os.path.splitext(os.path.basename(args.video_path))[0]
        input_dir = os.path.dirname(args.video_path)
        args.log_file = os.path.join(input_dir, f"{base_filename}{get_config().log_suffix}")

    # Setup logging
    log_level = getattr(__import__('logging'), args.log_level.upper())
    setup_logging(args.log_file, log_level)
    
    logger = get_logger()
    logger.info("Starting Video Cleaner CLI processing...")
    logger.info(f"Input video: {args.video_path}")
    logger.info(f"Output video: {args.output_path}")
    logger.info(f"Subtitle file: {args.srt_path or 'None (AI-only mode)'}")
    logger.info(f"Foul words file: {args.words_file}")
    logger.info(f"Whisper model: {args.model}")

    # Process the video
    success = process_video_cli(args)
    
    if success:
        print(f"\n✅ Processing completed successfully!")
        print(f"📁 Output video: {args.output_path}")
        print(f"📋 Log file: {args.log_file}")
    else:
        print(f"\n❌ Processing failed. Check the log file for details: {args.log_file}")
        sys.exit(1)


if __name__ == "__main__":
    main()
