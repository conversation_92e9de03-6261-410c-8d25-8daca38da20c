"""
Configuration management for video processing.
"""

from dataclasses import dataclass
from typing import Optional
import json
import os


@dataclass
class ProcessingConfig:
    """Configuration for video processing."""
    
    # Whisper model settings
    model_size: str = "medium"
    
    # Muting settings
    mute_padding_start: float = 0.05
    mute_padding_end: float = 0.05
    
    # File naming settings
    temp_audio_filename: str = "temp_audio_for_processing.wav"
    output_suffix: str = "_muted_verified"
    report_suffix: str = "_report.log"
    log_suffix: str = "_processing.log"
    
    # Audio extraction settings
    audio_codec: str = 'pcm_s16le'
    audio_sample_rate: int = 44100
    
    # Video encoding settings
    video_codec: str = 'libx264'
    video_preset: str = 'medium'
    video_crf: str = '18'
    audio_output_codec: str = 'aac'
    
    # Performance settings
    max_video_size_gb: float = 10.0
    chunk_duration_seconds: float = 300.0
    max_log_lines: int = 1000
    
    # Retry settings
    max_retry_attempts: int = 3
    retry_delay: float = 1.0
    retry_backoff: float = 2.0

    @classmethod
    def load_from_file(cls, filepath: str) -> 'ProcessingConfig':
        """Load configuration from JSON file."""
        if not os.path.exists(filepath):
            return cls()
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return cls(**data)
        except (json.JSONDecodeError, TypeError) as e:
            print(f"Warning: Could not load config from {filepath}: {e}")
            return cls()
    
    def save_to_file(self, filepath: str) -> bool:
        """Save configuration to JSON file."""
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.__dict__, f, indent=2)
            return True
        except Exception as e:
            print(f"Error saving config to {filepath}: {e}")
            return False


# Global configuration instance
config = ProcessingConfig()


def get_config() -> ProcessingConfig:
    """Get the global configuration instance."""
    return config


def set_config(new_config: ProcessingConfig) -> None:
    """Set the global configuration instance."""
    global config
    config = new_config
